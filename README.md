# 🔥 Userscripts Collection
*A set of quality-of-life enhancements for developers and power users*  

[![License](https://img.shields.io/github/license/yookibooki/userscripts)](https://github.com/yookibooki/userscripts/blob/main/LICENSE)

---

## 🚀 Quick Start  
1. Install a userscript manager (e.g. [Violentmonkey](https://violentmonkey.github.io))
2. Click any script link below  
3. Confirm installation  

---

## 📜 Script Catalog  

### 🔍 [GitHub Repository Size Checker](https://raw.githubusercontent.com/yookibooki/userscripts/main/github-repo-size/user.js)
▸ Displays actual code size (excluding `.git`) in GitHub repositories.  
🔑 **Pro Tip:** Use with Personal Access Token for higher rate limits.  

### 🌗 [Toggle Dark Mode](https://raw.githubusercontent.com/yookibooki/userscripts/main/toggle-dark-mode/user.js)
▸ Instantly toggle dark mode on any website with `Ctrl+Shift+D`  
🖥️ **Works with:** 95% of modern websites  

### 🔄 [Google Search Site Switch](https://raw.githubusercontent.com/yookibooki/userscripts/main/google-search-site-switch/user.js)  
▸ Adds quick-search links to rival engines (Yandex, Bing, DuckDuckGo) on Google results pages  

### 📰 [Medium to Freedium Redirector](https://raw.githubusercontent.com/yookibooki/userscripts/main/medium-to-freedium-redirector/user.js)
▸ Redirects Medium articles to Freedium, allowing you to read articles without a subscription.

---

## 🛠️ Userscript Managers  
Choose your browser-compatible manager:  

| Manager | Chrome | Firefox | Edge | Safari | Opera |  
|---------|--------|---------|------|--------|-------|  
| [Tampermonkey](https://www.tampermonkey.net) | ✓ | ✓ | ✓ | ✓ | ✓ |  
| [Violentmonkey](https://violentmonkey.github.io) | ✓ | ✓ | ✓ | ✗ | ✓ |  
| [Greasemonkey](https://www.greasespot.net) | ✗ | ✓ | ✗ | ✗ | ✗ |  

---

## 🎯 Features  
✔️ Lightweight (all scripts < 10KB)  
✔️ No tracking or analytics  
✔️ Regular maintenance updates  
✔️ Cross-browser compatibility  

---

## 🤝 Contributing  
We welcome:  
- 🐛 Bug reports  
- 💡 Feature requests  
- ✨ Pull requests  

[Open an Issue](https://github.com/yookibooki/userscripts/issues) | [View Project Board](https://github.com/users/yookibooki/projects/1)  

---

## 📜 License  
MIT © yookibooki - Free for personal and commercial use  