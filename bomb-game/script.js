const timeDisplay = document.getElementById('time');
const statusDisplay = document.getElementById('status');
const defuseBtn = document.getElementById('defuse-btn');

let timeLeft = 30;
let timerId = null;
let gameOver = false;

function updateTimer() {
    if (timeLeft > 0) {
        timeLeft--;
        timeDisplay.textContent = timeLeft;
    } else {
        clearInterval(timerId);
        gameOver = true;
        statusDisplay.textContent = '💥 Boom! The bomb exploded!';
        defuseBtn.disabled = true;
    }
}

defuseBtn.addEventListener('click', () => {
    if (!gameOver) {
        clearInterval(timerId);
        gameOver = true;
        statusDisplay.textContent = '✅ You defused the bomb! Well done!';
        defuseBtn.disabled = true;
    }
});

function startGame() {
    timerId = setInterval(updateTimer, 1000);
}

startGame();
