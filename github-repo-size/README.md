# 🔍 GitHub Repository Size Checker

[![Install](https://img.shields.io/badge/Install-Script-blue.svg)](https://raw.githubusercontent.com/yookibooki/userscripts/refs/heads/main/github-repo-size/user.js)
[![License](https://img.shields.io/github/license/yookibooki/userscripts)](../LICENSE)

## 📝 Description
Enhances GitHub repository pages by displaying the actual code size, excluding the `.git` directory and other non-source files. Get accurate insights into repository sizes without manual calculations.

![demo](https://i.imgur.com/td7Ieau.gif)

## 🚀 Setup Guide
1. Install a userscript manager (e.g. [Violentmonkey](https://violentmonkey.github.io))
2. [Install the script](https://raw.githubusercontent.com/yookibooki/userscripts/refs/heads/main/github-repo-size/user.js)
3. [Generate a Personal Access Token (PAT)](https://github.com/settings/personal-access-tokens/new?description=GitHub+Repository+Size+Checker) with `repo` scope
4. Enter your PAT when prompted on first use

## ⚠️ Note
A Personal Access Token is required to avoid GitHub API rate limiting. Your token is stored securely in your browser's local storage.